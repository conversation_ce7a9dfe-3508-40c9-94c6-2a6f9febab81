/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.15.11
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QListView>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QGridLayout *gridLayout_5;
    QHBoxLayout *horizontalLayout_11;
    QWidget *widget_2;
    QHBoxLayout *horizontalLayout_10;
    QHBoxLayout *horizontalLayout_9;
    QSpacerItem *horizontalSpacer_11;
    QLabel *label_channel;
    QLabel *label_fbl;
    QLabel *label_fps;
    QLabel *label_BM;
    QLabel *label_ML;
    QLabel *label_storge;
    QSpacerItem *horizontalSpacer_12;
    QWidget *recordingIndicatorWidget;
    QHBoxLayout *horizontalLayout_13;
    QHBoxLayout *horizontalLayout_12;
    QLabel *recordingDotLabel;
    QLabel *recordingTimeLabel;
    QGridLayout *gridLayout_4;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *verticalSpacer;
    QSpacerItem *verticalSpacer_2;
    QVBoxLayout *verticalLayout_3;
    QStackedWidget *stackedWidget;
    QWidget *menu;
    QGridLayout *gridLayout_3;
    QVBoxLayout *verticalLayout_4;
    QHBoxLayout *horizontalLayout_3;
    QLabel *man_menu;
    QSpacerItem *horizontalSpacer_3;
    QFrame *line;
    QHBoxLayout *horizontalLayout_2;
    QVBoxLayout *verticalLayout_2;
    QPushButton *camera;
    QPushButton *custom;
    QPushButton *video_file;
    QPushButton *view_pictures;
    QSpacerItem *horizontalSpacer;
    QWidget *page_imageparame;
    QVBoxLayout *verticalLayout_8;
    QVBoxLayout *verticalLayout_7;
    QVBoxLayout *verticalLayout_5;
    QHBoxLayout *horizontalLayout_14;
    QLabel *imageparame;
    QSpacerItem *horizontalSpacer_13;
    QFrame *line_4;
    QHBoxLayout *horizontalLayout_15;
    QVBoxLayout *verticalLayout_6;
    QPushButton *camera_UVC;
    QPushButton *camera_HDMI;
    QSpacerItem *horizontalSpacer_14;
    QSpacerItem *verticalSpacer_3;
    QWidget *UVC_imageset;
    QGridLayout *gridLayout_10;
    QVBoxLayout *verticalLayout_13;
    QHBoxLayout *horizontalLayout_16;
    QLabel *man_menu_4;
    QSpacerItem *horizontalSpacer_15;
    QFrame *line_5;
    QScrollArea *scrollArea_UVC;
    QWidget *scrollAreaWidgetContents;
    QGridLayout *gridLayout_11;
    QVBoxLayout *verticalLayout_14;
    QHBoxLayout *horizontalLayout_22;
    QPushButton *UVC_brightness;
    QSpacerItem *horizontalSpacer_21;
    QSpinBox *spinBox_UVC_brightness;
    QHBoxLayout *horizontalLayout_23;
    QPushButton *UVC_contrast;
    QSpacerItem *horizontalSpacer_22;
    QSpinBox *spinBox_UVC_contrast;
    QHBoxLayout *horizontalLayout_24;
    QPushButton *UVC_saturation;
    QSpacerItem *horizontalSpacer_23;
    QSpinBox *spinBox_UVC_saturation;
    QHBoxLayout *horizontalLayout_26;
    QPushButton *UVC_hue;
    QSpacerItem *horizontalSpacer_25;
    QSpinBox *spinBox_UVC_hue;
    QHBoxLayout *horizontalLayout_27;
    QPushButton *UVC_exposure_auto;
    QSpacerItem *horizontalSpacer_26;
    QCheckBox *checkBox_UVC_exposure;
    QHBoxLayout *horizontalLayout_28;
    QPushButton *UVC_exposure_2;
    QSpacerItem *horizontalSpacer_27;
    QSpinBox *spinBox_UVC_exposure;
    QHBoxLayout *horizontalLayout_29;
    QPushButton *UVC_white_auto;
    QSpacerItem *horizontalSpacer_28;
    QCheckBox *checkBox_UVC_white;
    QHBoxLayout *horizontalLayout_30;
    QPushButton *UVC_white;
    QSpacerItem *horizontalSpacer_29;
    QSpinBox *spinBox__UVC_white;
    QWidget *HDMI_imageset;
    QGridLayout *gridLayout_13;
    QHBoxLayout *horizontalLayout_17;
    QLabel *man_menu_5;
    QSpacerItem *horizontalSpacer_16;
    QFrame *line_6;
    QScrollArea *scrollArea_HDMI;
    QWidget *scrollAreaWidgetContents_2;
    QGridLayout *gridLayout_12;
    QVBoxLayout *verticalLayout_16;
    QHBoxLayout *horizontalLayout_31;
    QPushButton *HDMI_brightness;
    QSpacerItem *horizontalSpacer_30;
    QSpinBox *spinBox_HDMI_brightness;
    QHBoxLayout *horizontalLayout_32;
    QPushButton *HDMI_contrast;
    QSpacerItem *horizontalSpacer_31;
    QSpinBox *spinBox_HDMI_contrast;
    QHBoxLayout *horizontalLayout_33;
    QPushButton *HDMI_saturation;
    QSpacerItem *horizontalSpacer_32;
    QSpinBox *spinBox_HDMI_saturation;
    QHBoxLayout *horizontalLayout_34;
    QPushButton *HDMI_hue;
    QSpacerItem *horizontalSpacer_33;
    QSpinBox *spinBox_HDMI_hue;
    QHBoxLayout *horizontalLayout_35;
    QPushButton *HDMI_exposure_auto;
    QSpacerItem *horizontalSpacer_34;
    QCheckBox *checkBox_HDMI_exposure_auto;
    QHBoxLayout *horizontalLayout_36;
    QPushButton *HDMI_exposure;
    QSpacerItem *horizontalSpacer_35;
    QSpinBox *spinBox_HDMI_exposure;
    QHBoxLayout *horizontalLayout_37;
    QPushButton *HDMI_white_auto;
    QSpacerItem *horizontalSpacer_36;
    QCheckBox *checkBox_HDMI_white;
    QHBoxLayout *horizontalLayout_38;
    QPushButton *HDMI_white;
    QSpacerItem *horizontalSpacer_37;
    QSpinBox *spinBox_HDMI_white;
    QWidget *recording;
    QGridLayout *gridLayout_7;
    QGridLayout *gridLayout_6;
    QHBoxLayout *horizontalLayout_6;
    QLabel *man_menu_3;
    QSpacerItem *horizontalSpacer_8;
    QHBoxLayout *horizontalLayout_8;
    QPushButton *start_photo;
    QSpacerItem *horizontalSpacer_10;
    QLabel *is_photo;
    QHBoxLayout *horizontalLayout_7;
    QPushButton *start_audio;
    QSpacerItem *horizontalSpacer_9;
    QLabel *is_audio;
    QHBoxLayout *horizontalLayout_5;
    QPushButton *start_watermark;
    QSpacerItem *horizontalSpacer_7;
    QLabel *is_watermark;
    QHBoxLayout *horizontalLayout_4;
    QPushButton *start_recording;
    QSpacerItem *horizontalSpacer_6;
    QLabel *is_record;
    QFrame *line_3;
    QWidget *videofile;
    QGridLayout *gridLayout_8;
    QListView *listView_videofile;
    QWidget *viewpictures;
    QGridLayout *gridLayout_9;
    QListView *listView_viewpictures;
    QWidget *page_recordset;
    QVBoxLayout *verticalLayout_12;
    QVBoxLayout *verticalLayout_9;
    QVBoxLayout *verticalLayout_10;
    QHBoxLayout *horizontalLayout_18;
    QLabel *imageparame_2;
    QSpacerItem *horizontalSpacer_17;
    QFrame *line_7;
    QHBoxLayout *horizontalLayout_19;
    QVBoxLayout *verticalLayout_11;
    QPushButton *camera_3;
    QPushButton *custom_3;
    QSpacerItem *horizontalSpacer_18;
    QSpacerItem *verticalSpacer_4;
    QWidget *page;
    QFrame *line_8;
    QWidget *layoutWidget;
    QHBoxLayout *horizontalLayout_21;
    QVBoxLayout *verticalLayout_15;
    QPushButton *camera_4;
    QPushButton *custom_4;
    QSpacerItem *horizontalSpacer_20;
    QWidget *layoutWidget1;
    QHBoxLayout *horizontalLayout_20;
    QLabel *imageparame_3;
    QSpacerItem *horizontalSpacer_19;
    QWidget *help_menu;
    QGridLayout *gridLayout_2;
    QVBoxLayout *verticalLayout;
    QFrame *line_2;
    QHBoxLayout *horizontalLayout;
    QGridLayout *gridLayout;
    QLabel *Return;
    QLabel *select;
    QLabel *reset;
    QLabel *modification;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *horizontalSpacer_5;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(1542, 717);
        MainWindow->setMinimumSize(QSize(0, 0));
        MainWindow->setStyleSheet(QString::fromUtf8(""));
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(centralwidget->sizePolicy().hasHeightForWidth());
        centralwidget->setSizePolicy(sizePolicy);
        centralwidget->setStyleSheet(QString::fromUtf8(""));
        gridLayout_5 = new QGridLayout(centralwidget);
        gridLayout_5->setSpacing(0);
        gridLayout_5->setObjectName(QString::fromUtf8("gridLayout_5"));
        gridLayout_5->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_11 = new QHBoxLayout();
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        widget_2 = new QWidget(centralwidget);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setStyleSheet(QString::fromUtf8("background-color: transparent;"));
        horizontalLayout_10 = new QHBoxLayout(widget_2);
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        horizontalLayout_10->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_9 = new QHBoxLayout();
        horizontalLayout_9->setSpacing(5);
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        horizontalSpacer_11 = new QSpacerItem(520, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_9->addItem(horizontalSpacer_11);

        label_channel = new QLabel(widget_2);
        label_channel->setObjectName(QString::fromUtf8("label_channel"));
        QSizePolicy sizePolicy1(QSizePolicy::Fixed, QSizePolicy::Preferred);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(label_channel->sizePolicy().hasHeightForWidth());
        label_channel->setSizePolicy(sizePolicy1);
        label_channel->setMinimumSize(QSize(140, 0));
        label_channel->setMaximumSize(QSize(140, 16777215));
        QFont font;
        font.setPointSize(17);
        label_channel->setFont(font);

        horizontalLayout_9->addWidget(label_channel);

        label_fbl = new QLabel(widget_2);
        label_fbl->setObjectName(QString::fromUtf8("label_fbl"));
        label_fbl->setMinimumSize(QSize(140, 0));
        label_fbl->setMaximumSize(QSize(140, 16777215));
        label_fbl->setFont(font);

        horizontalLayout_9->addWidget(label_fbl);

        label_fps = new QLabel(widget_2);
        label_fps->setObjectName(QString::fromUtf8("label_fps"));
        label_fps->setMinimumSize(QSize(120, 0));
        label_fps->setMaximumSize(QSize(120, 16777215));
        label_fps->setFont(font);

        horizontalLayout_9->addWidget(label_fps);

        label_BM = new QLabel(widget_2);
        label_BM->setObjectName(QString::fromUtf8("label_BM"));
        label_BM->setMinimumSize(QSize(140, 0));
        label_BM->setMaximumSize(QSize(140, 16777215));
        label_BM->setFont(font);

        horizontalLayout_9->addWidget(label_BM);

        label_ML = new QLabel(widget_2);
        label_ML->setObjectName(QString::fromUtf8("label_ML"));
        label_ML->setMinimumSize(QSize(140, 0));
        label_ML->setMaximumSize(QSize(140, 16777215));
        label_ML->setFont(font);

        horizontalLayout_9->addWidget(label_ML);

        label_storge = new QLabel(widget_2);
        label_storge->setObjectName(QString::fromUtf8("label_storge"));
        label_storge->setMinimumSize(QSize(180, 0));
        label_storge->setMaximumSize(QSize(180, 16777215));
        label_storge->setFont(font);

        horizontalLayout_9->addWidget(label_storge);


        horizontalLayout_10->addLayout(horizontalLayout_9);


        horizontalLayout_11->addWidget(widget_2);

        horizontalSpacer_12 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_11->addItem(horizontalSpacer_12);

        recordingIndicatorWidget = new QWidget(centralwidget);
        recordingIndicatorWidget->setObjectName(QString::fromUtf8("recordingIndicatorWidget"));
        recordingIndicatorWidget->setStyleSheet(QString::fromUtf8("background-color: transparent;"));
        horizontalLayout_13 = new QHBoxLayout(recordingIndicatorWidget);
        horizontalLayout_13->setSpacing(0);
        horizontalLayout_13->setObjectName(QString::fromUtf8("horizontalLayout_13"));
        horizontalLayout_13->setContentsMargins(0, 0, 6, 0);
        horizontalLayout_12 = new QHBoxLayout();
        horizontalLayout_12->setObjectName(QString::fromUtf8("horizontalLayout_12"));
        recordingDotLabel = new QLabel(recordingIndicatorWidget);
        recordingDotLabel->setObjectName(QString::fromUtf8("recordingDotLabel"));
        recordingDotLabel->setMinimumSize(QSize(20, 20));
        recordingDotLabel->setMaximumSize(QSize(20, 20));
        recordingDotLabel->setStyleSheet(QString::fromUtf8("background-color: red;\n"
"border-radius: 10px;"));

        horizontalLayout_12->addWidget(recordingDotLabel);

        recordingTimeLabel = new QLabel(recordingIndicatorWidget);
        recordingTimeLabel->setObjectName(QString::fromUtf8("recordingTimeLabel"));
        recordingTimeLabel->setMinimumSize(QSize(0, 0));
        recordingTimeLabel->setMaximumSize(QSize(16777215, 16777215));
        QFont font1;
        font1.setPointSize(16);
        font1.setBold(true);
        recordingTimeLabel->setFont(font1);
        recordingTimeLabel->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: transparent;"));

        horizontalLayout_12->addWidget(recordingTimeLabel);


        horizontalLayout_13->addLayout(horizontalLayout_12);


        horizontalLayout_11->addWidget(recordingIndicatorWidget);


        gridLayout_5->addLayout(horizontalLayout_11, 0, 0, 1, 1);

        gridLayout_4 = new QGridLayout();
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        horizontalSpacer_4 = new QSpacerItem(171, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_4->addItem(horizontalSpacer_4, 3, 3, 1, 1);

        verticalSpacer = new QSpacerItem(20, 85, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_4->addItem(verticalSpacer, 0, 2, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 85, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_4->addItem(verticalSpacer_2, 4, 1, 1, 1);

        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setSpacing(0);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        stackedWidget = new QStackedWidget(centralwidget);
        stackedWidget->setObjectName(QString::fromUtf8("stackedWidget"));
        stackedWidget->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        menu = new QWidget();
        menu->setObjectName(QString::fromUtf8("menu"));
        menu->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        gridLayout_3 = new QGridLayout(menu);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setSpacing(10);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(-1, -1, -1, 10);
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        man_menu = new QLabel(menu);
        man_menu->setObjectName(QString::fromUtf8("man_menu"));
        man_menu->setMinimumSize(QSize(0, 60));
        QFont font2;
        font2.setPointSize(31);
        man_menu->setFont(font2);
        man_menu->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_3->addWidget(man_menu);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_3);


        verticalLayout_4->addLayout(horizontalLayout_3);

        line = new QFrame(menu);
        line->setObjectName(QString::fromUtf8("line"));
        line->setMinimumSize(QSize(0, 0));
        line->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line->setMidLineWidth(5);
        line->setFrameShape(QFrame::HLine);
        line->setFrameShadow(QFrame::Sunken);

        verticalLayout_4->addWidget(line);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        camera = new QPushButton(menu);
        camera->setObjectName(QString::fromUtf8("camera"));
        camera->setMinimumSize(QSize(0, 50));
        camera->setFont(font2);
        camera->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        camera->setCheckable(true);
        camera->setAutoRepeat(false);
        camera->setAutoExclusive(false);

        verticalLayout_2->addWidget(camera);

        custom = new QPushButton(menu);
        custom->setObjectName(QString::fromUtf8("custom"));
        custom->setMinimumSize(QSize(0, 50));
        custom->setFont(font2);
        custom->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        custom->setCheckable(true);

        verticalLayout_2->addWidget(custom);

        video_file = new QPushButton(menu);
        video_file->setObjectName(QString::fromUtf8("video_file"));
        video_file->setMinimumSize(QSize(0, 50));
        video_file->setFont(font2);
        video_file->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        video_file->setCheckable(true);

        verticalLayout_2->addWidget(video_file);

        view_pictures = new QPushButton(menu);
        view_pictures->setObjectName(QString::fromUtf8("view_pictures"));
        view_pictures->setMinimumSize(QSize(220, 50));
        view_pictures->setFont(font2);
        view_pictures->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        view_pictures->setCheckable(true);

        verticalLayout_2->addWidget(view_pictures);


        horizontalLayout_2->addLayout(verticalLayout_2);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);


        verticalLayout_4->addLayout(horizontalLayout_2);


        gridLayout_3->addLayout(verticalLayout_4, 0, 0, 1, 1);

        stackedWidget->addWidget(menu);
        page_imageparame = new QWidget();
        page_imageparame->setObjectName(QString::fromUtf8("page_imageparame"));
        verticalLayout_8 = new QVBoxLayout(page_imageparame);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        verticalLayout_8->setContentsMargins(0, 0, 0, 0);
        verticalLayout_7 = new QVBoxLayout();
        verticalLayout_7->setObjectName(QString::fromUtf8("verticalLayout_7"));
        verticalLayout_5 = new QVBoxLayout();
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        horizontalLayout_14 = new QHBoxLayout();
        horizontalLayout_14->setObjectName(QString::fromUtf8("horizontalLayout_14"));
        horizontalLayout_14->setSizeConstraint(QLayout::SetFixedSize);
        imageparame = new QLabel(page_imageparame);
        imageparame->setObjectName(QString::fromUtf8("imageparame"));
        QSizePolicy sizePolicy2(QSizePolicy::Preferred, QSizePolicy::Fixed);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(imageparame->sizePolicy().hasHeightForWidth());
        imageparame->setSizePolicy(sizePolicy2);
        imageparame->setMinimumSize(QSize(0, 60));
        imageparame->setMaximumSize(QSize(16777215, 60));
        imageparame->setFont(font2);
        imageparame->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_14->addWidget(imageparame);

        horizontalSpacer_13 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_13);


        verticalLayout_5->addLayout(horizontalLayout_14);

        line_4 = new QFrame(page_imageparame);
        line_4->setObjectName(QString::fromUtf8("line_4"));
        line_4->setMinimumSize(QSize(0, 0));
        line_4->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_4->setMidLineWidth(5);
        line_4->setFrameShape(QFrame::HLine);
        line_4->setFrameShadow(QFrame::Sunken);

        verticalLayout_5->addWidget(line_4);

        horizontalLayout_15 = new QHBoxLayout();
        horizontalLayout_15->setObjectName(QString::fromUtf8("horizontalLayout_15"));
        verticalLayout_6 = new QVBoxLayout();
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        camera_UVC = new QPushButton(page_imageparame);
        camera_UVC->setObjectName(QString::fromUtf8("camera_UVC"));
        camera_UVC->setMinimumSize(QSize(0, 50));
        camera_UVC->setFont(font2);
        camera_UVC->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        camera_UVC->setCheckable(true);
        camera_UVC->setAutoRepeat(false);
        camera_UVC->setAutoExclusive(false);

        verticalLayout_6->addWidget(camera_UVC);

        camera_HDMI = new QPushButton(page_imageparame);
        camera_HDMI->setObjectName(QString::fromUtf8("camera_HDMI"));
        camera_HDMI->setMinimumSize(QSize(0, 50));
        camera_HDMI->setFont(font2);
        camera_HDMI->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        camera_HDMI->setCheckable(true);

        verticalLayout_6->addWidget(camera_HDMI);


        horizontalLayout_15->addLayout(verticalLayout_6);

        horizontalSpacer_14 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_15->addItem(horizontalSpacer_14);


        verticalLayout_5->addLayout(horizontalLayout_15);


        verticalLayout_7->addLayout(verticalLayout_5);

        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_7->addItem(verticalSpacer_3);


        verticalLayout_8->addLayout(verticalLayout_7);

        stackedWidget->addWidget(page_imageparame);
        UVC_imageset = new QWidget();
        UVC_imageset->setObjectName(QString::fromUtf8("UVC_imageset"));
        gridLayout_10 = new QGridLayout(UVC_imageset);
        gridLayout_10->setObjectName(QString::fromUtf8("gridLayout_10"));
        verticalLayout_13 = new QVBoxLayout();
        verticalLayout_13->setObjectName(QString::fromUtf8("verticalLayout_13"));
        horizontalLayout_16 = new QHBoxLayout();
        horizontalLayout_16->setObjectName(QString::fromUtf8("horizontalLayout_16"));
        horizontalLayout_16->setSizeConstraint(QLayout::SetFixedSize);
        man_menu_4 = new QLabel(UVC_imageset);
        man_menu_4->setObjectName(QString::fromUtf8("man_menu_4"));
        sizePolicy2.setHeightForWidth(man_menu_4->sizePolicy().hasHeightForWidth());
        man_menu_4->setSizePolicy(sizePolicy2);
        man_menu_4->setMinimumSize(QSize(0, 60));
        man_menu_4->setMaximumSize(QSize(16777215, 60));
        man_menu_4->setFont(font2);
        man_menu_4->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_16->addWidget(man_menu_4);

        horizontalSpacer_15 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_15);


        verticalLayout_13->addLayout(horizontalLayout_16);

        line_5 = new QFrame(UVC_imageset);
        line_5->setObjectName(QString::fromUtf8("line_5"));
        line_5->setMinimumSize(QSize(0, 0));
        line_5->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_5->setMidLineWidth(5);
        line_5->setFrameShape(QFrame::HLine);
        line_5->setFrameShadow(QFrame::Sunken);

        verticalLayout_13->addWidget(line_5);


        gridLayout_10->addLayout(verticalLayout_13, 0, 0, 1, 1);

        scrollArea_UVC = new QScrollArea(UVC_imageset);
        scrollArea_UVC->setObjectName(QString::fromUtf8("scrollArea_UVC"));
        scrollArea_UVC->setMinimumSize(QSize(0, 0));
        scrollArea_UVC->setLineWidth(1);
        scrollArea_UVC->setMidLineWidth(0);
        scrollArea_UVC->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        scrollArea_UVC->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        scrollArea_UVC->setWidgetResizable(true);
        scrollAreaWidgetContents = new QWidget();
        scrollAreaWidgetContents->setObjectName(QString::fromUtf8("scrollAreaWidgetContents"));
        scrollAreaWidgetContents->setGeometry(QRect(0, 0, 849, 478));
        gridLayout_11 = new QGridLayout(scrollAreaWidgetContents);
        gridLayout_11->setObjectName(QString::fromUtf8("gridLayout_11"));
        verticalLayout_14 = new QVBoxLayout();
        verticalLayout_14->setObjectName(QString::fromUtf8("verticalLayout_14"));
        horizontalLayout_22 = new QHBoxLayout();
        horizontalLayout_22->setObjectName(QString::fromUtf8("horizontalLayout_22"));
        UVC_brightness = new QPushButton(scrollAreaWidgetContents);
        UVC_brightness->setObjectName(QString::fromUtf8("UVC_brightness"));
        UVC_brightness->setMinimumSize(QSize(220, 50));
        UVC_brightness->setMaximumSize(QSize(220, 50));
        UVC_brightness->setFont(font2);
        UVC_brightness->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_brightness->setCheckable(true);

        horizontalLayout_22->addWidget(UVC_brightness);

        horizontalSpacer_21 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_22->addItem(horizontalSpacer_21);

        spinBox_UVC_brightness = new QSpinBox(scrollAreaWidgetContents);
        spinBox_UVC_brightness->setObjectName(QString::fromUtf8("spinBox_UVC_brightness"));
        spinBox_UVC_brightness->setMinimumSize(QSize(0, 0));
        spinBox_UVC_brightness->setMaximumSize(QSize(100, 30));
        QFont font3;
        font3.setPointSize(11);
        spinBox_UVC_brightness->setFont(font3);
        spinBox_UVC_brightness->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));
        spinBox_UVC_brightness->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_22->addWidget(spinBox_UVC_brightness);


        verticalLayout_14->addLayout(horizontalLayout_22);

        horizontalLayout_23 = new QHBoxLayout();
        horizontalLayout_23->setObjectName(QString::fromUtf8("horizontalLayout_23"));
        UVC_contrast = new QPushButton(scrollAreaWidgetContents);
        UVC_contrast->setObjectName(QString::fromUtf8("UVC_contrast"));
        UVC_contrast->setMinimumSize(QSize(220, 50));
        UVC_contrast->setMaximumSize(QSize(220, 50));
        UVC_contrast->setFont(font2);
        UVC_contrast->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_contrast->setCheckable(true);

        horizontalLayout_23->addWidget(UVC_contrast);

        horizontalSpacer_22 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_23->addItem(horizontalSpacer_22);

        spinBox_UVC_contrast = new QSpinBox(scrollAreaWidgetContents);
        spinBox_UVC_contrast->setObjectName(QString::fromUtf8("spinBox_UVC_contrast"));
        spinBox_UVC_contrast->setMaximumSize(QSize(100, 30));
        spinBox_UVC_contrast->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_23->addWidget(spinBox_UVC_contrast);


        verticalLayout_14->addLayout(horizontalLayout_23);

        horizontalLayout_24 = new QHBoxLayout();
        horizontalLayout_24->setObjectName(QString::fromUtf8("horizontalLayout_24"));
        UVC_saturation = new QPushButton(scrollAreaWidgetContents);
        UVC_saturation->setObjectName(QString::fromUtf8("UVC_saturation"));
        UVC_saturation->setMinimumSize(QSize(220, 50));
        UVC_saturation->setMaximumSize(QSize(220, 50));
        UVC_saturation->setFont(font2);
        UVC_saturation->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_saturation->setCheckable(true);

        horizontalLayout_24->addWidget(UVC_saturation);

        horizontalSpacer_23 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_24->addItem(horizontalSpacer_23);

        spinBox_UVC_saturation = new QSpinBox(scrollAreaWidgetContents);
        spinBox_UVC_saturation->setObjectName(QString::fromUtf8("spinBox_UVC_saturation"));
        spinBox_UVC_saturation->setMaximumSize(QSize(100, 30));
        spinBox_UVC_saturation->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_24->addWidget(spinBox_UVC_saturation);


        verticalLayout_14->addLayout(horizontalLayout_24);

        horizontalLayout_26 = new QHBoxLayout();
        horizontalLayout_26->setObjectName(QString::fromUtf8("horizontalLayout_26"));
        UVC_hue = new QPushButton(scrollAreaWidgetContents);
        UVC_hue->setObjectName(QString::fromUtf8("UVC_hue"));
        UVC_hue->setMinimumSize(QSize(220, 50));
        UVC_hue->setMaximumSize(QSize(220, 50));
        UVC_hue->setFont(font2);
        UVC_hue->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_hue->setCheckable(true);

        horizontalLayout_26->addWidget(UVC_hue);

        horizontalSpacer_25 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_26->addItem(horizontalSpacer_25);

        spinBox_UVC_hue = new QSpinBox(scrollAreaWidgetContents);
        spinBox_UVC_hue->setObjectName(QString::fromUtf8("spinBox_UVC_hue"));
        spinBox_UVC_hue->setMaximumSize(QSize(100, 30));
        spinBox_UVC_hue->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_26->addWidget(spinBox_UVC_hue);


        verticalLayout_14->addLayout(horizontalLayout_26);

        horizontalLayout_27 = new QHBoxLayout();
        horizontalLayout_27->setObjectName(QString::fromUtf8("horizontalLayout_27"));
        UVC_exposure_auto = new QPushButton(scrollAreaWidgetContents);
        UVC_exposure_auto->setObjectName(QString::fromUtf8("UVC_exposure_auto"));
        UVC_exposure_auto->setMinimumSize(QSize(220, 50));
        UVC_exposure_auto->setMaximumSize(QSize(220, 50));
        UVC_exposure_auto->setFont(font2);
        UVC_exposure_auto->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_exposure_auto->setCheckable(true);

        horizontalLayout_27->addWidget(UVC_exposure_auto);

        horizontalSpacer_26 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_27->addItem(horizontalSpacer_26);

        checkBox_UVC_exposure = new QCheckBox(scrollAreaWidgetContents);
        checkBox_UVC_exposure->setObjectName(QString::fromUtf8("checkBox_UVC_exposure"));
        QSizePolicy sizePolicy3(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy3.setHorizontalStretch(0);
        sizePolicy3.setVerticalStretch(0);
        sizePolicy3.setHeightForWidth(checkBox_UVC_exposure->sizePolicy().hasHeightForWidth());
        checkBox_UVC_exposure->setSizePolicy(sizePolicy3);
        checkBox_UVC_exposure->setMaximumSize(QSize(100, 30));
        checkBox_UVC_exposure->setStyleSheet(QString::fromUtf8(""));
        checkBox_UVC_exposure->setIconSize(QSize(30, 30));
        checkBox_UVC_exposure->setChecked(true);

        horizontalLayout_27->addWidget(checkBox_UVC_exposure);


        verticalLayout_14->addLayout(horizontalLayout_27);

        horizontalLayout_28 = new QHBoxLayout();
        horizontalLayout_28->setObjectName(QString::fromUtf8("horizontalLayout_28"));
        UVC_exposure_2 = new QPushButton(scrollAreaWidgetContents);
        UVC_exposure_2->setObjectName(QString::fromUtf8("UVC_exposure_2"));
        UVC_exposure_2->setMinimumSize(QSize(220, 50));
        UVC_exposure_2->setMaximumSize(QSize(220, 50));
        UVC_exposure_2->setFont(font2);
        UVC_exposure_2->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_exposure_2->setCheckable(true);

        horizontalLayout_28->addWidget(UVC_exposure_2);

        horizontalSpacer_27 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_28->addItem(horizontalSpacer_27);

        spinBox_UVC_exposure = new QSpinBox(scrollAreaWidgetContents);
        spinBox_UVC_exposure->setObjectName(QString::fromUtf8("spinBox_UVC_exposure"));
        spinBox_UVC_exposure->setMaximumSize(QSize(100, 30));
        spinBox_UVC_exposure->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_28->addWidget(spinBox_UVC_exposure);


        verticalLayout_14->addLayout(horizontalLayout_28);

        horizontalLayout_29 = new QHBoxLayout();
        horizontalLayout_29->setObjectName(QString::fromUtf8("horizontalLayout_29"));
        UVC_white_auto = new QPushButton(scrollAreaWidgetContents);
        UVC_white_auto->setObjectName(QString::fromUtf8("UVC_white_auto"));
        UVC_white_auto->setMinimumSize(QSize(220, 50));
        UVC_white_auto->setMaximumSize(QSize(220, 50));
        UVC_white_auto->setFont(font2);
        UVC_white_auto->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_white_auto->setCheckable(true);

        horizontalLayout_29->addWidget(UVC_white_auto);

        horizontalSpacer_28 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_29->addItem(horizontalSpacer_28);

        checkBox_UVC_white = new QCheckBox(scrollAreaWidgetContents);
        checkBox_UVC_white->setObjectName(QString::fromUtf8("checkBox_UVC_white"));
        sizePolicy3.setHeightForWidth(checkBox_UVC_white->sizePolicy().hasHeightForWidth());
        checkBox_UVC_white->setSizePolicy(sizePolicy3);
        checkBox_UVC_white->setMaximumSize(QSize(100, 30));
        checkBox_UVC_white->setStyleSheet(QString::fromUtf8(""));
        checkBox_UVC_white->setIconSize(QSize(30, 30));
        checkBox_UVC_white->setChecked(true);

        horizontalLayout_29->addWidget(checkBox_UVC_white);


        verticalLayout_14->addLayout(horizontalLayout_29);

        horizontalLayout_30 = new QHBoxLayout();
        horizontalLayout_30->setObjectName(QString::fromUtf8("horizontalLayout_30"));
        UVC_white = new QPushButton(scrollAreaWidgetContents);
        UVC_white->setObjectName(QString::fromUtf8("UVC_white"));
        UVC_white->setMinimumSize(QSize(220, 50));
        UVC_white->setMaximumSize(QSize(220, 50));
        UVC_white->setFont(font2);
        UVC_white->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_white->setCheckable(true);

        horizontalLayout_30->addWidget(UVC_white);

        horizontalSpacer_29 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_30->addItem(horizontalSpacer_29);

        spinBox__UVC_white = new QSpinBox(scrollAreaWidgetContents);
        spinBox__UVC_white->setObjectName(QString::fromUtf8("spinBox__UVC_white"));
        spinBox__UVC_white->setMaximumSize(QSize(100, 30));
        spinBox__UVC_white->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_30->addWidget(spinBox__UVC_white);


        verticalLayout_14->addLayout(horizontalLayout_30);


        gridLayout_11->addLayout(verticalLayout_14, 0, 0, 1, 1);

        scrollArea_UVC->setWidget(scrollAreaWidgetContents);

        gridLayout_10->addWidget(scrollArea_UVC, 1, 0, 1, 1);

        stackedWidget->addWidget(UVC_imageset);
        HDMI_imageset = new QWidget();
        HDMI_imageset->setObjectName(QString::fromUtf8("HDMI_imageset"));
        gridLayout_13 = new QGridLayout(HDMI_imageset);
        gridLayout_13->setObjectName(QString::fromUtf8("gridLayout_13"));
        horizontalLayout_17 = new QHBoxLayout();
        horizontalLayout_17->setObjectName(QString::fromUtf8("horizontalLayout_17"));
        horizontalLayout_17->setSizeConstraint(QLayout::SetFixedSize);
        man_menu_5 = new QLabel(HDMI_imageset);
        man_menu_5->setObjectName(QString::fromUtf8("man_menu_5"));
        sizePolicy2.setHeightForWidth(man_menu_5->sizePolicy().hasHeightForWidth());
        man_menu_5->setSizePolicy(sizePolicy2);
        man_menu_5->setMinimumSize(QSize(0, 60));
        man_menu_5->setMaximumSize(QSize(16777215, 60));
        man_menu_5->setFont(font2);
        man_menu_5->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_17->addWidget(man_menu_5);

        horizontalSpacer_16 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_17->addItem(horizontalSpacer_16);


        gridLayout_13->addLayout(horizontalLayout_17, 0, 0, 1, 1);

        line_6 = new QFrame(HDMI_imageset);
        line_6->setObjectName(QString::fromUtf8("line_6"));
        line_6->setMinimumSize(QSize(0, 0));
        line_6->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_6->setMidLineWidth(5);
        line_6->setFrameShape(QFrame::HLine);
        line_6->setFrameShadow(QFrame::Sunken);

        gridLayout_13->addWidget(line_6, 1, 0, 1, 1);

        scrollArea_HDMI = new QScrollArea(HDMI_imageset);
        scrollArea_HDMI->setObjectName(QString::fromUtf8("scrollArea_HDMI"));
        scrollArea_HDMI->setMinimumSize(QSize(0, 0));
        scrollArea_HDMI->setLineWidth(1);
        scrollArea_HDMI->setMidLineWidth(0);
        scrollArea_HDMI->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        scrollArea_HDMI->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        scrollArea_HDMI->setWidgetResizable(true);
        scrollAreaWidgetContents_2 = new QWidget();
        scrollAreaWidgetContents_2->setObjectName(QString::fromUtf8("scrollAreaWidgetContents_2"));
        scrollAreaWidgetContents_2->setGeometry(QRect(0, 0, 849, 478));
        gridLayout_12 = new QGridLayout(scrollAreaWidgetContents_2);
        gridLayout_12->setObjectName(QString::fromUtf8("gridLayout_12"));
        verticalLayout_16 = new QVBoxLayout();
        verticalLayout_16->setObjectName(QString::fromUtf8("verticalLayout_16"));
        horizontalLayout_31 = new QHBoxLayout();
        horizontalLayout_31->setObjectName(QString::fromUtf8("horizontalLayout_31"));
        HDMI_brightness = new QPushButton(scrollAreaWidgetContents_2);
        HDMI_brightness->setObjectName(QString::fromUtf8("HDMI_brightness"));
        HDMI_brightness->setMinimumSize(QSize(220, 50));
        HDMI_brightness->setMaximumSize(QSize(220, 50));
        HDMI_brightness->setFont(font2);
        HDMI_brightness->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_brightness->setCheckable(true);

        horizontalLayout_31->addWidget(HDMI_brightness);

        horizontalSpacer_30 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_31->addItem(horizontalSpacer_30);

        spinBox_HDMI_brightness = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_HDMI_brightness->setObjectName(QString::fromUtf8("spinBox_HDMI_brightness"));
        spinBox_HDMI_brightness->setMinimumSize(QSize(0, 0));
        spinBox_HDMI_brightness->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_brightness->setFont(font3);
        spinBox_HDMI_brightness->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));
        spinBox_HDMI_brightness->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_31->addWidget(spinBox_HDMI_brightness);


        verticalLayout_16->addLayout(horizontalLayout_31);

        horizontalLayout_32 = new QHBoxLayout();
        horizontalLayout_32->setObjectName(QString::fromUtf8("horizontalLayout_32"));
        HDMI_contrast = new QPushButton(scrollAreaWidgetContents_2);
        HDMI_contrast->setObjectName(QString::fromUtf8("HDMI_contrast"));
        HDMI_contrast->setMinimumSize(QSize(220, 50));
        HDMI_contrast->setMaximumSize(QSize(220, 50));
        HDMI_contrast->setFont(font2);
        HDMI_contrast->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_contrast->setCheckable(true);

        horizontalLayout_32->addWidget(HDMI_contrast);

        horizontalSpacer_31 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_32->addItem(horizontalSpacer_31);

        spinBox_HDMI_contrast = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_HDMI_contrast->setObjectName(QString::fromUtf8("spinBox_HDMI_contrast"));
        spinBox_HDMI_contrast->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_contrast->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_32->addWidget(spinBox_HDMI_contrast);


        verticalLayout_16->addLayout(horizontalLayout_32);

        horizontalLayout_33 = new QHBoxLayout();
        horizontalLayout_33->setObjectName(QString::fromUtf8("horizontalLayout_33"));
        HDMI_saturation = new QPushButton(scrollAreaWidgetContents_2);
        HDMI_saturation->setObjectName(QString::fromUtf8("HDMI_saturation"));
        HDMI_saturation->setMinimumSize(QSize(220, 50));
        HDMI_saturation->setMaximumSize(QSize(220, 50));
        HDMI_saturation->setFont(font2);
        HDMI_saturation->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_saturation->setCheckable(true);

        horizontalLayout_33->addWidget(HDMI_saturation);

        horizontalSpacer_32 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_33->addItem(horizontalSpacer_32);

        spinBox_HDMI_saturation = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_HDMI_saturation->setObjectName(QString::fromUtf8("spinBox_HDMI_saturation"));
        spinBox_HDMI_saturation->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_saturation->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_33->addWidget(spinBox_HDMI_saturation);


        verticalLayout_16->addLayout(horizontalLayout_33);

        horizontalLayout_34 = new QHBoxLayout();
        horizontalLayout_34->setObjectName(QString::fromUtf8("horizontalLayout_34"));
        HDMI_hue = new QPushButton(scrollAreaWidgetContents_2);
        HDMI_hue->setObjectName(QString::fromUtf8("HDMI_hue"));
        HDMI_hue->setMinimumSize(QSize(220, 50));
        HDMI_hue->setMaximumSize(QSize(220, 50));
        HDMI_hue->setFont(font2);
        HDMI_hue->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_hue->setCheckable(true);

        horizontalLayout_34->addWidget(HDMI_hue);

        horizontalSpacer_33 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_34->addItem(horizontalSpacer_33);

        spinBox_HDMI_hue = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_HDMI_hue->setObjectName(QString::fromUtf8("spinBox_HDMI_hue"));
        spinBox_HDMI_hue->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_hue->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_34->addWidget(spinBox_HDMI_hue);


        verticalLayout_16->addLayout(horizontalLayout_34);

        horizontalLayout_35 = new QHBoxLayout();
        horizontalLayout_35->setObjectName(QString::fromUtf8("horizontalLayout_35"));
        HDMI_exposure_auto = new QPushButton(scrollAreaWidgetContents_2);
        HDMI_exposure_auto->setObjectName(QString::fromUtf8("HDMI_exposure_auto"));
        HDMI_exposure_auto->setMinimumSize(QSize(220, 50));
        HDMI_exposure_auto->setMaximumSize(QSize(220, 50));
        HDMI_exposure_auto->setFont(font2);
        HDMI_exposure_auto->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_exposure_auto->setCheckable(true);

        horizontalLayout_35->addWidget(HDMI_exposure_auto);

        horizontalSpacer_34 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_35->addItem(horizontalSpacer_34);

        checkBox_HDMI_exposure_auto = new QCheckBox(scrollAreaWidgetContents_2);
        checkBox_HDMI_exposure_auto->setObjectName(QString::fromUtf8("checkBox_HDMI_exposure_auto"));
        sizePolicy3.setHeightForWidth(checkBox_HDMI_exposure_auto->sizePolicy().hasHeightForWidth());
        checkBox_HDMI_exposure_auto->setSizePolicy(sizePolicy3);
        checkBox_HDMI_exposure_auto->setMaximumSize(QSize(100, 30));
        checkBox_HDMI_exposure_auto->setStyleSheet(QString::fromUtf8(""));
        checkBox_HDMI_exposure_auto->setIconSize(QSize(30, 30));
        checkBox_HDMI_exposure_auto->setChecked(true);

        horizontalLayout_35->addWidget(checkBox_HDMI_exposure_auto);


        verticalLayout_16->addLayout(horizontalLayout_35);

        horizontalLayout_36 = new QHBoxLayout();
        horizontalLayout_36->setObjectName(QString::fromUtf8("horizontalLayout_36"));
        HDMI_exposure = new QPushButton(scrollAreaWidgetContents_2);
        HDMI_exposure->setObjectName(QString::fromUtf8("HDMI_exposure"));
        HDMI_exposure->setMinimumSize(QSize(220, 50));
        HDMI_exposure->setMaximumSize(QSize(220, 50));
        HDMI_exposure->setFont(font2);
        HDMI_exposure->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_exposure->setCheckable(true);

        horizontalLayout_36->addWidget(HDMI_exposure);

        horizontalSpacer_35 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_36->addItem(horizontalSpacer_35);

        spinBox_HDMI_exposure = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_HDMI_exposure->setObjectName(QString::fromUtf8("spinBox_HDMI_exposure"));
        spinBox_HDMI_exposure->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_exposure->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_36->addWidget(spinBox_HDMI_exposure);


        verticalLayout_16->addLayout(horizontalLayout_36);

        horizontalLayout_37 = new QHBoxLayout();
        horizontalLayout_37->setObjectName(QString::fromUtf8("horizontalLayout_37"));
        HDMI_white_auto = new QPushButton(scrollAreaWidgetContents_2);
        HDMI_white_auto->setObjectName(QString::fromUtf8("HDMI_white_auto"));
        HDMI_white_auto->setMinimumSize(QSize(220, 50));
        HDMI_white_auto->setMaximumSize(QSize(220, 50));
        HDMI_white_auto->setFont(font2);
        HDMI_white_auto->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_white_auto->setCheckable(true);

        horizontalLayout_37->addWidget(HDMI_white_auto);

        horizontalSpacer_36 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_37->addItem(horizontalSpacer_36);

        checkBox_HDMI_white = new QCheckBox(scrollAreaWidgetContents_2);
        checkBox_HDMI_white->setObjectName(QString::fromUtf8("checkBox_HDMI_white"));
        sizePolicy3.setHeightForWidth(checkBox_HDMI_white->sizePolicy().hasHeightForWidth());
        checkBox_HDMI_white->setSizePolicy(sizePolicy3);
        checkBox_HDMI_white->setMaximumSize(QSize(100, 30));
        checkBox_HDMI_white->setStyleSheet(QString::fromUtf8(""));
        checkBox_HDMI_white->setIconSize(QSize(30, 30));
        checkBox_HDMI_white->setChecked(true);

        horizontalLayout_37->addWidget(checkBox_HDMI_white);


        verticalLayout_16->addLayout(horizontalLayout_37);

        horizontalLayout_38 = new QHBoxLayout();
        horizontalLayout_38->setObjectName(QString::fromUtf8("horizontalLayout_38"));
        HDMI_white = new QPushButton(scrollAreaWidgetContents_2);
        HDMI_white->setObjectName(QString::fromUtf8("HDMI_white"));
        HDMI_white->setMinimumSize(QSize(220, 50));
        HDMI_white->setMaximumSize(QSize(220, 50));
        HDMI_white->setFont(font2);
        HDMI_white->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_white->setCheckable(true);

        horizontalLayout_38->addWidget(HDMI_white);

        horizontalSpacer_37 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_38->addItem(horizontalSpacer_37);

        spinBox_HDMI_white = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_HDMI_white->setObjectName(QString::fromUtf8("spinBox_HDMI_white"));
        spinBox_HDMI_white->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_white->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_38->addWidget(spinBox_HDMI_white);


        verticalLayout_16->addLayout(horizontalLayout_38);


        gridLayout_12->addLayout(verticalLayout_16, 0, 0, 1, 1);

        scrollArea_HDMI->setWidget(scrollAreaWidgetContents_2);

        gridLayout_13->addWidget(scrollArea_HDMI, 2, 0, 1, 1);

        stackedWidget->addWidget(HDMI_imageset);
        recording = new QWidget();
        recording->setObjectName(QString::fromUtf8("recording"));
        recording->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        gridLayout_7 = new QGridLayout(recording);
        gridLayout_7->setObjectName(QString::fromUtf8("gridLayout_7"));
        gridLayout_6 = new QGridLayout();
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        gridLayout_6->setHorizontalSpacing(0);
        gridLayout_6->setVerticalSpacing(10);
        gridLayout_6->setContentsMargins(-1, -1, -1, 0);
        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        man_menu_3 = new QLabel(recording);
        man_menu_3->setObjectName(QString::fromUtf8("man_menu_3"));
        man_menu_3->setMinimumSize(QSize(0, 60));
        man_menu_3->setMaximumSize(QSize(16777215, 60));
        man_menu_3->setFont(font2);
        man_menu_3->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_6->addWidget(man_menu_3);

        horizontalSpacer_8 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_8);


        gridLayout_6->addLayout(horizontalLayout_6, 0, 0, 1, 1);

        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        start_photo = new QPushButton(recording);
        start_photo->setObjectName(QString::fromUtf8("start_photo"));
        start_photo->setMinimumSize(QSize(220, 50));
        start_photo->setMaximumSize(QSize(220, 50));
        start_photo->setFont(font2);
        start_photo->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_photo->setCheckable(true);

        horizontalLayout_8->addWidget(start_photo);

        horizontalSpacer_10 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_8->addItem(horizontalSpacer_10);

        is_photo = new QLabel(recording);
        is_photo->setObjectName(QString::fromUtf8("is_photo"));
        is_photo->setMinimumSize(QSize(0, 50));
        is_photo->setFont(font2);
        is_photo->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        is_photo->setAlignment(Qt::AlignCenter);

        horizontalLayout_8->addWidget(is_photo);


        gridLayout_6->addLayout(horizontalLayout_8, 5, 0, 1, 1);

        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        start_audio = new QPushButton(recording);
        start_audio->setObjectName(QString::fromUtf8("start_audio"));
        start_audio->setMinimumSize(QSize(220, 50));
        start_audio->setMaximumSize(QSize(220, 50));
        start_audio->setFont(font2);
        start_audio->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_audio->setCheckable(true);

        horizontalLayout_7->addWidget(start_audio);

        horizontalSpacer_9 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_9);

        is_audio = new QLabel(recording);
        is_audio->setObjectName(QString::fromUtf8("is_audio"));
        is_audio->setMinimumSize(QSize(0, 50));
        is_audio->setFont(font2);
        is_audio->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        is_audio->setAlignment(Qt::AlignCenter);

        horizontalLayout_7->addWidget(is_audio);


        gridLayout_6->addLayout(horizontalLayout_7, 4, 0, 1, 1);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        start_watermark = new QPushButton(recording);
        start_watermark->setObjectName(QString::fromUtf8("start_watermark"));
        start_watermark->setMinimumSize(QSize(220, 50));
        start_watermark->setMaximumSize(QSize(220, 50));
        start_watermark->setFont(font2);
        start_watermark->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_watermark->setCheckable(true);

        horizontalLayout_5->addWidget(start_watermark);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_7);

        is_watermark = new QLabel(recording);
        is_watermark->setObjectName(QString::fromUtf8("is_watermark"));
        is_watermark->setMinimumSize(QSize(0, 50));
        is_watermark->setFont(font2);
        is_watermark->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        is_watermark->setAlignment(Qt::AlignCenter);

        horizontalLayout_5->addWidget(is_watermark);


        gridLayout_6->addLayout(horizontalLayout_5, 3, 0, 1, 1);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        start_recording = new QPushButton(recording);
        start_recording->setObjectName(QString::fromUtf8("start_recording"));
        start_recording->setMinimumSize(QSize(220, 50));
        start_recording->setMaximumSize(QSize(220, 50));
        start_recording->setFont(font2);
        start_recording->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_recording->setCheckable(true);

        horizontalLayout_4->addWidget(start_recording);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_6);

        is_record = new QLabel(recording);
        is_record->setObjectName(QString::fromUtf8("is_record"));
        is_record->setMinimumSize(QSize(0, 50));
        is_record->setFont(font2);
        is_record->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        is_record->setAlignment(Qt::AlignCenter);

        horizontalLayout_4->addWidget(is_record);


        gridLayout_6->addLayout(horizontalLayout_4, 2, 0, 1, 1);

        line_3 = new QFrame(recording);
        line_3->setObjectName(QString::fromUtf8("line_3"));
        line_3->setMinimumSize(QSize(0, 0));
        line_3->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_3->setMidLineWidth(5);
        line_3->setFrameShape(QFrame::HLine);
        line_3->setFrameShadow(QFrame::Sunken);

        gridLayout_6->addWidget(line_3, 1, 0, 1, 1);


        gridLayout_7->addLayout(gridLayout_6, 0, 0, 1, 1);

        stackedWidget->addWidget(recording);
        videofile = new QWidget();
        videofile->setObjectName(QString::fromUtf8("videofile"));
        gridLayout_8 = new QGridLayout(videofile);
        gridLayout_8->setSpacing(0);
        gridLayout_8->setObjectName(QString::fromUtf8("gridLayout_8"));
        gridLayout_8->setContentsMargins(0, 0, 0, 0);
        listView_videofile = new QListView(videofile);
        listView_videofile->setObjectName(QString::fromUtf8("listView_videofile"));
        listView_videofile->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));

        gridLayout_8->addWidget(listView_videofile, 0, 0, 1, 1);

        stackedWidget->addWidget(videofile);
        viewpictures = new QWidget();
        viewpictures->setObjectName(QString::fromUtf8("viewpictures"));
        gridLayout_9 = new QGridLayout(viewpictures);
        gridLayout_9->setSpacing(0);
        gridLayout_9->setObjectName(QString::fromUtf8("gridLayout_9"));
        gridLayout_9->setContentsMargins(0, 0, 0, 0);
        listView_viewpictures = new QListView(viewpictures);
        listView_viewpictures->setObjectName(QString::fromUtf8("listView_viewpictures"));
        listView_viewpictures->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));

        gridLayout_9->addWidget(listView_viewpictures, 0, 0, 1, 1);

        stackedWidget->addWidget(viewpictures);
        page_recordset = new QWidget();
        page_recordset->setObjectName(QString::fromUtf8("page_recordset"));
        verticalLayout_12 = new QVBoxLayout(page_recordset);
        verticalLayout_12->setSpacing(0);
        verticalLayout_12->setObjectName(QString::fromUtf8("verticalLayout_12"));
        verticalLayout_12->setContentsMargins(0, 0, 0, 0);
        verticalLayout_9 = new QVBoxLayout();
        verticalLayout_9->setObjectName(QString::fromUtf8("verticalLayout_9"));
        verticalLayout_10 = new QVBoxLayout();
        verticalLayout_10->setObjectName(QString::fromUtf8("verticalLayout_10"));
        horizontalLayout_18 = new QHBoxLayout();
        horizontalLayout_18->setObjectName(QString::fromUtf8("horizontalLayout_18"));
        horizontalLayout_18->setSizeConstraint(QLayout::SetFixedSize);
        imageparame_2 = new QLabel(page_recordset);
        imageparame_2->setObjectName(QString::fromUtf8("imageparame_2"));
        sizePolicy2.setHeightForWidth(imageparame_2->sizePolicy().hasHeightForWidth());
        imageparame_2->setSizePolicy(sizePolicy2);
        imageparame_2->setMinimumSize(QSize(0, 60));
        imageparame_2->setMaximumSize(QSize(16777215, 60));
        imageparame_2->setFont(font2);
        imageparame_2->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_18->addWidget(imageparame_2);

        horizontalSpacer_17 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_18->addItem(horizontalSpacer_17);


        verticalLayout_10->addLayout(horizontalLayout_18);

        line_7 = new QFrame(page_recordset);
        line_7->setObjectName(QString::fromUtf8("line_7"));
        line_7->setMinimumSize(QSize(0, 0));
        line_7->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_7->setMidLineWidth(5);
        line_7->setFrameShape(QFrame::HLine);
        line_7->setFrameShadow(QFrame::Sunken);

        verticalLayout_10->addWidget(line_7);

        horizontalLayout_19 = new QHBoxLayout();
        horizontalLayout_19->setObjectName(QString::fromUtf8("horizontalLayout_19"));
        verticalLayout_11 = new QVBoxLayout();
        verticalLayout_11->setObjectName(QString::fromUtf8("verticalLayout_11"));
        camera_3 = new QPushButton(page_recordset);
        camera_3->setObjectName(QString::fromUtf8("camera_3"));
        camera_3->setMinimumSize(QSize(0, 50));
        camera_3->setFont(font2);
        camera_3->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        camera_3->setCheckable(true);
        camera_3->setAutoRepeat(false);
        camera_3->setAutoExclusive(false);

        verticalLayout_11->addWidget(camera_3);

        custom_3 = new QPushButton(page_recordset);
        custom_3->setObjectName(QString::fromUtf8("custom_3"));
        custom_3->setMinimumSize(QSize(0, 50));
        custom_3->setFont(font2);
        custom_3->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        custom_3->setCheckable(true);

        verticalLayout_11->addWidget(custom_3);


        horizontalLayout_19->addLayout(verticalLayout_11);

        horizontalSpacer_18 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_19->addItem(horizontalSpacer_18);


        verticalLayout_10->addLayout(horizontalLayout_19);


        verticalLayout_9->addLayout(verticalLayout_10);

        verticalSpacer_4 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_9->addItem(verticalSpacer_4);


        verticalLayout_12->addLayout(verticalLayout_9);

        stackedWidget->addWidget(page_recordset);
        page = new QWidget();
        page->setObjectName(QString::fromUtf8("page"));
        line_8 = new QFrame(page);
        line_8->setObjectName(QString::fromUtf8("line_8"));
        line_8->setGeometry(QRect(342, 140, 212, 16));
        line_8->setMinimumSize(QSize(0, 0));
        line_8->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_8->setMidLineWidth(5);
        line_8->setFrameShape(QFrame::HLine);
        line_8->setFrameShadow(QFrame::Sunken);
        layoutWidget = new QWidget(page);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(510, 180, 155, 110));
        horizontalLayout_21 = new QHBoxLayout(layoutWidget);
        horizontalLayout_21->setObjectName(QString::fromUtf8("horizontalLayout_21"));
        horizontalLayout_21->setContentsMargins(0, 0, 0, 0);
        verticalLayout_15 = new QVBoxLayout();
        verticalLayout_15->setObjectName(QString::fromUtf8("verticalLayout_15"));
        camera_4 = new QPushButton(layoutWidget);
        camera_4->setObjectName(QString::fromUtf8("camera_4"));
        camera_4->setMinimumSize(QSize(0, 50));
        camera_4->setFont(font2);
        camera_4->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        camera_4->setCheckable(true);
        camera_4->setAutoRepeat(false);
        camera_4->setAutoExclusive(false);

        verticalLayout_15->addWidget(camera_4);

        custom_4 = new QPushButton(layoutWidget);
        custom_4->setObjectName(QString::fromUtf8("custom_4"));
        custom_4->setMinimumSize(QSize(0, 50));
        custom_4->setFont(font2);
        custom_4->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"padding-left: 10px;\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        custom_4->setCheckable(true);

        verticalLayout_15->addWidget(custom_4);


        horizontalLayout_21->addLayout(verticalLayout_15);

        horizontalSpacer_20 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_21->addItem(horizontalSpacer_20);

        layoutWidget1 = new QWidget(page);
        layoutWidget1->setObjectName(QString::fromUtf8("layoutWidget1"));
        layoutWidget1->setGeometry(QRect(0, 0, 212, 62));
        horizontalLayout_20 = new QHBoxLayout(layoutWidget1);
        horizontalLayout_20->setObjectName(QString::fromUtf8("horizontalLayout_20"));
        horizontalLayout_20->setSizeConstraint(QLayout::SetFixedSize);
        horizontalLayout_20->setContentsMargins(0, 0, 0, 0);
        imageparame_3 = new QLabel(layoutWidget1);
        imageparame_3->setObjectName(QString::fromUtf8("imageparame_3"));
        sizePolicy2.setHeightForWidth(imageparame_3->sizePolicy().hasHeightForWidth());
        imageparame_3->setSizePolicy(sizePolicy2);
        imageparame_3->setMinimumSize(QSize(0, 60));
        imageparame_3->setMaximumSize(QSize(16777215, 60));
        imageparame_3->setFont(font2);
        imageparame_3->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_20->addWidget(imageparame_3);

        horizontalSpacer_19 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_20->addItem(horizontalSpacer_19);

        stackedWidget->addWidget(page);

        verticalLayout_3->addWidget(stackedWidget);

        help_menu = new QWidget(centralwidget);
        help_menu->setObjectName(QString::fromUtf8("help_menu"));
        help_menu->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        gridLayout_2 = new QGridLayout(help_menu);
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        line_2 = new QFrame(help_menu);
        line_2->setObjectName(QString::fromUtf8("line_2"));
        line_2->setMinimumSize(QSize(0, 0));
        line_2->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_2->setMidLineWidth(5);
        line_2->setFrameShape(QFrame::HLine);
        line_2->setFrameShadow(QFrame::Sunken);

        verticalLayout->addWidget(line_2);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        Return = new QLabel(help_menu);
        Return->setObjectName(QString::fromUtf8("Return"));
        Return->setMinimumSize(QSize(0, 50));
        QFont font4;
        font4.setPointSize(20);
        Return->setFont(font4);
        Return->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(Return, 1, 1, 1, 1);

        select = new QLabel(help_menu);
        select->setObjectName(QString::fromUtf8("select"));
        select->setMinimumSize(QSize(0, 50));
        select->setFont(font4);
        select->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(select, 0, 0, 1, 1);

        reset = new QLabel(help_menu);
        reset->setObjectName(QString::fromUtf8("reset"));
        reset->setMinimumSize(QSize(0, 50));
        reset->setFont(font4);
        reset->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(reset, 1, 0, 1, 1);

        modification = new QLabel(help_menu);
        modification->setObjectName(QString::fromUtf8("modification"));
        modification->setMinimumSize(QSize(0, 50));
        modification->setFont(font4);
        modification->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(modification, 0, 1, 1, 1);


        horizontalLayout->addLayout(gridLayout);

        horizontalSpacer_2 = new QSpacerItem(582, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);


        verticalLayout->addLayout(horizontalLayout);


        gridLayout_2->addLayout(verticalLayout, 0, 0, 1, 1);


        verticalLayout_3->addWidget(help_menu);


        gridLayout_4->addLayout(verticalLayout_3, 1, 1, 3, 2);

        horizontalSpacer_5 = new QSpacerItem(171, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_4->addItem(horizontalSpacer_5, 1, 0, 1, 1);


        gridLayout_5->addLayout(gridLayout_4, 1, 0, 1, 1);

        MainWindow->setCentralWidget(centralwidget);

        retranslateUi(MainWindow);

        stackedWidget->setCurrentIndex(3);


        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "MainWindow", nullptr));
        label_channel->setText(QCoreApplication::translate("MainWindow", "\351\200\232\351\201\223\357\274\232UVC", nullptr));
        label_fbl->setText(QCoreApplication::translate("MainWindow", "\345\210\206\350\276\250\347\216\207\357\274\2324K", nullptr));
        label_fps->setText(QCoreApplication::translate("MainWindow", "\345\270\247\347\216\207\357\274\23260", nullptr));
        label_BM->setText(QCoreApplication::translate("MainWindow", "\347\274\226\347\240\201\357\274\232H264", nullptr));
        label_ML->setText(QCoreApplication::translate("MainWindow", "\347\240\201\346\265\201\357\274\23240MB", nullptr));
        label_storge->setText(QCoreApplication::translate("MainWindow", "\345\255\230\345\202\250\344\275\215\347\275\256\357\274\232TF\345\215\241", nullptr));
        recordingDotLabel->setText(QString());
        recordingTimeLabel->setText(QCoreApplication::translate("MainWindow", "00:00:00", nullptr));
        man_menu->setText(QCoreApplication::translate("MainWindow", "\344\270\273\350\217\234\345\215\225", nullptr));
        camera->setText(QCoreApplication::translate("MainWindow", "\345\233\276\345\203\217\345\217\202\346\225\260", nullptr));
        custom->setText(QCoreApplication::translate("MainWindow", "\345\275\225\345\203\217\350\256\276\347\275\256", nullptr));
        video_file->setText(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266\347\256\241\347\220\206", nullptr));
        view_pictures->setText(QCoreApplication::translate("MainWindow", "\347\263\273\347\273\237\350\256\276\347\275\256", nullptr));
        imageparame->setText(QCoreApplication::translate("MainWindow", "\345\233\276\345\203\217\345\217\202\346\225\260", nullptr));
        camera_UVC->setText(QCoreApplication::translate("MainWindow", "UVC", nullptr));
        camera_HDMI->setText(QCoreApplication::translate("MainWindow", "HDMI", nullptr));
        man_menu_4->setText(QCoreApplication::translate("MainWindow", "UVC\345\233\276\345\203\217\345\217\202\346\225\260", nullptr));
        UVC_brightness->setText(QCoreApplication::translate("MainWindow", "\344\272\256\345\272\246", nullptr));
        UVC_contrast->setText(QCoreApplication::translate("MainWindow", "\345\257\271\346\257\224\345\272\246", nullptr));
        UVC_saturation->setText(QCoreApplication::translate("MainWindow", "\351\245\261\345\222\214\345\272\246", nullptr));
        UVC_hue->setText(QCoreApplication::translate("MainWindow", "\350\211\262\350\260\203", nullptr));
        UVC_exposure_auto->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250\346\233\235\345\205\211", nullptr));
        checkBox_UVC_exposure->setText(QCoreApplication::translate("MainWindow", "\346\230\257\345\220\246\350\207\252\345\212\250", nullptr));
        UVC_exposure_2->setText(QCoreApplication::translate("MainWindow", "\346\233\235\345\205\211", nullptr));
        UVC_white_auto->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250\347\231\275\345\271\263\350\241\241", nullptr));
        checkBox_UVC_white->setText(QCoreApplication::translate("MainWindow", "\346\230\257\345\220\246\350\207\252\345\212\250", nullptr));
        UVC_white->setText(QCoreApplication::translate("MainWindow", "\347\231\275\345\271\263\350\241\241", nullptr));
        man_menu_5->setText(QCoreApplication::translate("MainWindow", "HDMI\345\233\276\345\203\217\345\217\202\346\225\260", nullptr));
        HDMI_brightness->setText(QCoreApplication::translate("MainWindow", "\344\272\256\345\272\246", nullptr));
        HDMI_contrast->setText(QCoreApplication::translate("MainWindow", "\345\257\271\346\257\224\345\272\246", nullptr));
        HDMI_saturation->setText(QCoreApplication::translate("MainWindow", "\351\245\261\345\222\214\345\272\246", nullptr));
        HDMI_hue->setText(QCoreApplication::translate("MainWindow", "\350\211\262\350\260\203", nullptr));
        HDMI_exposure_auto->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250\346\233\235\345\205\211", nullptr));
        checkBox_HDMI_exposure_auto->setText(QCoreApplication::translate("MainWindow", "\346\230\257\345\220\246\350\207\252\345\212\250", nullptr));
        HDMI_exposure->setText(QCoreApplication::translate("MainWindow", "\346\233\235\345\205\211", nullptr));
        HDMI_white_auto->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250\347\231\275\345\271\263\350\241\241", nullptr));
        checkBox_HDMI_white->setText(QCoreApplication::translate("MainWindow", "\346\230\257\345\220\246\350\207\252\345\212\250", nullptr));
        HDMI_white->setText(QCoreApplication::translate("MainWindow", "\347\231\275\345\271\263\350\241\241", nullptr));
        man_menu_3->setText(QCoreApplication::translate("MainWindow", "\345\212\237\350\203\275", nullptr));
        start_photo->setText(QCoreApplication::translate("MainWindow", "\346\213\215\347\205\247", nullptr));
        is_photo->setText(QCoreApplication::translate("MainWindow", "< \346\230\257\345\220\246\346\213\215\347\205\247 >", nullptr));
        start_audio->setText(QCoreApplication::translate("MainWindow", "\351\237\263\351\242\221", nullptr));
        is_audio->setText(QCoreApplication::translate("MainWindow", "< N >", nullptr));
        start_watermark->setText(QCoreApplication::translate("MainWindow", "\346\260\264\345\215\260", nullptr));
        is_watermark->setText(QCoreApplication::translate("MainWindow", "< N >", nullptr));
        start_recording->setText(QCoreApplication::translate("MainWindow", "\345\275\225\345\203\217", nullptr));
        is_record->setText(QCoreApplication::translate("MainWindow", "< N >", nullptr));
        imageparame_2->setText(QCoreApplication::translate("MainWindow", "\345\275\225\345\203\217\350\256\276\347\275\256", nullptr));
        camera_3->setText(QCoreApplication::translate("MainWindow", "UVC", nullptr));
        custom_3->setText(QCoreApplication::translate("MainWindow", "HDMI", nullptr));
        camera_4->setText(QCoreApplication::translate("MainWindow", "UVC", nullptr));
        custom_4->setText(QCoreApplication::translate("MainWindow", "HDMI", nullptr));
        imageparame_3->setText(QCoreApplication::translate("MainWindow", "\345\233\276\345\203\217\345\217\202\346\225\260", nullptr));
        Return->setText(QCoreApplication::translate("MainWindow", "[Menu]\350\277\224\345\233\236", nullptr));
        select->setText(QCoreApplication::translate("MainWindow", "[\342\254\206 \342\254\207]\351\200\211\346\213\251", nullptr));
        reset->setText(QCoreApplication::translate("MainWindow", "[Reset]\347\241\256\345\256\232", nullptr));
        modification->setText(QCoreApplication::translate("MainWindow", "[\342\254\205 \342\236\241]\344\277\256\346\224\271", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
